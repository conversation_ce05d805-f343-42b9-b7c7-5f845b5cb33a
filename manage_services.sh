#!/bin/bash

# SaverProxyBot Service Management Script

SERVICES=("saverproxybot.socket" "saverproxybot.service" "saverproxybot-bots.service")

show_usage() {
    echo "Usage: $0 {start|stop|restart|status|logs|enable|disable}"
    echo ""
    echo "Commands:"
    echo "  start    - Start all services"
    echo "  stop     - Stop all services"
    echo "  restart  - Restart all services"
    echo "  status   - Show status of all services"
    echo "  logs     - Show logs for all services"
    echo "  enable   - Enable services to start on boot"
    echo "  disable  - Disable services from starting on boot"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs"
    echo "  $0 status"
}

start_services() {
    echo "🚀 Starting SaverProxyBot services..."
    
    # Start socket first (this will auto-start the main service when needed)
    systemctl start saverproxybot.socket
    echo "✅ Started saverproxybot.socket"
    
    # Wait a moment for socket to be ready
    sleep 2
    
    # Start the bots service
    systemctl start saverproxybot-bots.service
    echo "✅ Started saverproxybot-bots.service"
    
    echo "🎉 All services started successfully!"
}

stop_services() {
    echo "🛑 Stopping SaverProxyBot services..."
    
    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet "$service"; then
            systemctl stop "$service"
            echo "✅ Stopped $service"
        else
            echo "ℹ️  $service was not running"
        fi
    done
    
    echo "🎉 All services stopped!"
}

restart_services() {
    echo "🔄 Restarting SaverProxyBot services..."
    
    # Restart main service
    systemctl restart saverproxybot.service
    echo "✅ Restarted saverproxybot.service"
    
    # Restart bots service
    systemctl restart saverproxybot-bots.service
    echo "✅ Restarted saverproxybot-bots.service"
    
    echo "🎉 All services restarted successfully!"
}

show_status() {
    echo "📊 SaverProxyBot Service Status"
    echo "==============================="
    
    for service in "${SERVICES[@]}"; do
        echo ""
        echo "🔍 $service:"
        systemctl status "$service" --no-pager -l
        echo ""
    done
}

show_logs() {
    echo "📝 SaverProxyBot Service Logs"
    echo "============================="
    echo ""
    echo "Press Ctrl+C to exit log viewing"
    echo ""
    
    # Follow logs for all services
    journalctl -u saverproxybot.socket -u saverproxybot.service -u saverproxybot-bots.service -f
}

enable_services() {
    echo "✅ Enabling SaverProxyBot services..."
    
    for service in "${SERVICES[@]}"; do
        systemctl enable "$service"
        echo "✅ Enabled $service"
    done
    
    echo "🎉 All services enabled for startup!"
}

disable_services() {
    echo "❌ Disabling SaverProxyBot services..."
    
    for service in "${SERVICES[@]}"; do
        systemctl disable "$service"
        echo "❌ Disabled $service"
    done
    
    echo "🎉 All services disabled from startup!"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root (use sudo)"
   exit 1
fi

# Parse command line argument
case "$1" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    enable)
        enable_services
        ;;
    disable)
        disable_services
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

exit 0
