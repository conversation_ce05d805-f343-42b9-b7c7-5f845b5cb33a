# SaverProxyBot Deployment Files

This document lists all the deployment files created for the SaverProxyBot project.

## 📋 Deployment Files Overview

### 1. Systemd Service Files

#### `saverproxybot.socket`
- **Purpose**: Unix socket configuration for the Django application
- **Location**: Copy to `/etc/systemd/system/saverproxybot.socket`
- **Features**: 
  - Creates Unix socket at `/run/saverproxybot/saverproxybot.sock`
  - Runs as `www-data` user/group
  - Socket-based activation

#### `saverproxybot.service`
- **Purpose**: Main Django application service using Gunicorn + ASGI
- **Location**: Copy to `/etc/systemd/system/saverproxybot.service`
- **Features**:
  - Runs Django with Gun<PERSON> and Uvicorn workers
  - 3 worker processes with auto-restart
  - Security hardening enabled
  - Logging to `/var/log/saverproxybot/`

#### `saverproxybot-bots.service`
- **Purpose**: Background service for Telegram user bots
- **Location**: Copy to `/etc/systemd/system/saverproxybot-bots.service`
- **Features**:
  - Runs the `run_user_bots` Django management command
  - Auto-restart on failure
  - Depends on main service and database

### 2. Deployment Scripts

#### `deploy.sh`
- **Purpose**: Automated deployment script
- **Usage**: `sudo ./deploy.sh`
- **Features**:
  - Installs system dependencies
  - Creates necessary directories
  - Sets up systemd services
  - Configures nginx
  - Runs Django migrations
  - Creates `.env` template

#### `manage_services.sh`
- **Purpose**: Service management utility
- **Usage**: `sudo ./manage_services.sh {start|stop|restart|status|logs|enable|disable}`
- **Features**:
  - Easy service control
  - Status monitoring
  - Log viewing
  - Service enable/disable

#### `status.sh`
- **Purpose**: System status checker
- **Usage**: `sudo ./status.sh`
- **Features**:
  - Service status overview
  - Process monitoring
  - Network status check
  - Error log summary
  - HTTP response test

#### `backup.sh`
- **Purpose**: Backup utility
- **Usage**: `sudo ./backup.sh`
- **Features**:
  - Project files backup
  - Database backup
  - Service configuration backup
  - Automatic cleanup of old backups

### 3. Documentation

#### `README_DEPLOYMENT.md`
- **Purpose**: Comprehensive deployment guide
- **Contents**:
  - Step-by-step deployment instructions
  - Configuration details
  - Troubleshooting guide
  - Security considerations
  - Performance tuning tips

#### `DEPLOYMENT_FILES.md` (this file)
- **Purpose**: Overview of all deployment files
- **Contents**: File descriptions and usage instructions

## 🚀 Quick Start

1. **Make scripts executable:**
   ```bash
   chmod +x *.sh
   ```

2. **Run deployment:**
   ```bash
   sudo ./deploy.sh
   ```

3. **Configure environment:**
   ```bash
   sudo nano .env
   ```

4. **Start services:**
   ```bash
   sudo ./manage_services.sh start
   ```

5. **Check status:**
   ```bash
   sudo ./status.sh
   ```

## 📁 File Structure After Deployment

```
/root/apps/saverproxybot/
├── saverproxybot.socket          # Socket configuration
├── saverproxybot.service         # Main service configuration
├── saverproxybot-bots.service    # Bot service configuration
├── deploy.sh                     # Deployment script
├── manage_services.sh            # Service management
├── status.sh                     # Status checker
├── backup.sh                     # Backup utility
├── README_DEPLOYMENT.md          # Deployment guide
├── DEPLOYMENT_FILES.md           # This file
├── .env                          # Environment variables (created by deploy.sh)
└── requirements.txt              # Updated with gunicorn/uvicorn

/etc/systemd/system/
├── saverproxybot.socket          # Installed socket file
├── saverproxybot.service         # Installed service file
└── saverproxybot-bots.service    # Installed bot service file

/var/log/saverproxybot/
├── access.log                    # Gunicorn access logs
└── error.log                     # Gunicorn error logs

/run/saverproxybot/
└── saverproxybot.sock            # Unix socket file

/etc/nginx/sites-available/
└── saverproxybot                 # Nginx configuration
```

## 🔧 Service Dependencies

```
saverproxybot.socket
├── saverproxybot.service (auto-started by socket)
└── saverproxybot-bots.service (manual start)

Dependencies:
├── postgresql.service
├── redis.service
└── network.target
```

## 📊 Monitoring Commands

```bash
# Service status
sudo ./status.sh

# View logs
sudo ./manage_services.sh logs

# Restart services
sudo ./manage_services.sh restart

# Create backup
sudo ./backup.sh
```

## 🔒 Security Features

- Services run as `www-data` user (non-root)
- Unix socket communication (no network exposure)
- Systemd security hardening enabled
- Read-only filesystem protection
- Private temporary directories
- Kernel protection features

## 🎯 Production Checklist

- [ ] Configure `.env` with production values
- [ ] Set up SSL certificate
- [ ] Configure firewall rules
- [ ] Set up log rotation
- [ ] Configure monitoring
- [ ] Test backup/restore procedures
- [ ] Update domain in nginx configuration
- [ ] Set up database backups
- [ ] Configure Redis persistence
- [ ] Test service auto-restart
