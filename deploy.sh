#!/bin/bash

# SaverProxyBot Deployment Script
# This script deploys the Django application as systemd services with Unix socket

set -e

PROJECT_DIR="/root/apps/saverproxybot"
SERVICE_USER="www-data"
LOG_DIR="/var/log/saverproxybot"
RUN_DIR="/run/saverproxybot"

echo "🚀 Starting SaverProxyBot deployment..."

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root"
   exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p "$LOG_DIR"
mkdir -p "$RUN_DIR"
chown -R $SERVICE_USER:$SERVICE_USER "$LOG_DIR"
chown -R $SERVICE_USER:$SERVICE_USER "$RUN_DIR"

# Install system dependencies
echo "📦 Installing system dependencies..."
apt-get update
apt-get install -y python3-pip python3-venv postgresql postgresql-contrib redis-server nginx

# Install gunicorn and uvicorn in the virtual environment
echo "🐍 Installing Python dependencies..."
cd "$PROJECT_DIR"

# Activate virtual environment and install additional dependencies
source venv/bin/activate
pip install gunicorn uvicorn[standard]
deactivate

# Create .env file template if it doesn't exist
if [ ! -f "$PROJECT_DIR/.env" ]; then
    echo "📝 Creating .env template..."
    cat > "$PROJECT_DIR/.env" << EOF
# Database Configuration
DATABASE_URL=postgresql://saverproxybot:your_password@localhost:5432/saverproxybot

# Django Settings
DEBUG=False
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Redis Configuration
REDIS_URL=redis://127.0.0.1:6379/0

# Telegram API Configuration
API_ID=your_api_id
API_HASH=your_api_hash

# Bot Configuration
X_AUTH_TOKEN=your_auth_token
EOF
    echo "⚠️  Please edit $PROJECT_DIR/.env with your actual configuration values"
fi

# Set proper permissions
echo "🔐 Setting permissions..."
chown -R $SERVICE_USER:$SERVICE_USER "$PROJECT_DIR"
chmod +x "$PROJECT_DIR/manage.py"

# Copy systemd service files
echo "📋 Installing systemd service files..."
cp "$PROJECT_DIR/saverproxybot.socket" /etc/systemd/system/
cp "$PROJECT_DIR/saverproxybot.service" /etc/systemd/system/
cp "$PROJECT_DIR/saverproxybot-bots.service" /etc/systemd/system/

# Reload systemd
echo "🔄 Reloading systemd..."
systemctl daemon-reload

# Enable services
echo "✅ Enabling services..."
systemctl enable saverproxybot.socket
systemctl enable saverproxybot.service
systemctl enable saverproxybot-bots.service

# Start socket (this will start the main service when needed)
echo "🚀 Starting services..."
systemctl start saverproxybot.socket

# Wait a moment and start the bots service
sleep 2
systemctl start saverproxybot-bots.service

# Create nginx configuration
echo "🌐 Creating nginx configuration..."
cat > /etc/nginx/sites-available/saverproxybot << EOF
server {
    listen 80;
    server_name localhost;  # Change this to your domain

    client_max_body_size 100M;

    location / {
        proxy_pass http://unix:/run/saverproxybot/saverproxybot.sock;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    location /static/ {
        alias $PROJECT_DIR/staticfiles/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        alias $PROJECT_DIR/media/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Enable nginx site
ln -sf /etc/nginx/sites-available/saverproxybot /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
nginx -t

# Restart nginx
systemctl restart nginx
systemctl enable nginx

# Run Django migrations and collect static files
echo "🗄️  Running Django setup..."
cd "$PROJECT_DIR"
sudo -u $SERVICE_USER venv/bin/python manage.py migrate
sudo -u $SERVICE_USER venv/bin/python manage.py collectstatic --noinput

# Show status
echo "📊 Service status:"
systemctl status saverproxybot.socket --no-pager -l
systemctl status saverproxybot.service --no-pager -l
systemctl status saverproxybot-bots.service --no-pager -l

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Service Management Commands:"
echo "  Start services:    sudo systemctl start saverproxybot.socket saverproxybot-bots.service"
echo "  Stop services:     sudo systemctl stop saverproxybot.socket saverproxybot.service saverproxybot-bots.service"
echo "  Restart services:  sudo systemctl restart saverproxybot.service saverproxybot-bots.service"
echo "  View logs:         sudo journalctl -u saverproxybot.service -f"
echo "  View bot logs:     sudo journalctl -u saverproxybot-bots.service -f"
echo ""
echo "🌐 Your application should be available at: http://localhost"
echo ""
echo "⚠️  Don't forget to:"
echo "  1. Edit $PROJECT_DIR/.env with your actual configuration"
echo "  2. Set up your PostgreSQL database"
echo "  3. Configure your domain in nginx configuration"
echo "  4. Set up SSL certificate for production"
