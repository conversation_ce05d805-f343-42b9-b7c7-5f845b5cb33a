#!/bin/bash

# SaverProxyBot Backup Script

PROJECT_DIR="/root/apps/saverproxybot"
BACKUP_DIR="/root/backups/saverproxybot"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="saverproxybot_backup_$DATE"

echo "💾 SaverProxyBot Backup Script"
echo "=============================="

# Create backup directory
mkdir -p "$BACKUP_DIR"

echo "📁 Creating backup: $BACKUP_NAME"

# Create backup archive
cd /root/apps
tar -czf "$BACKUP_DIR/$BACKUP_NAME.tar.gz" \
    --exclude='saverproxybot/venv' \
    --exclude='saverproxybot/__pycache__' \
    --exclude='saverproxybot/*/__pycache__' \
    --exclude='saverproxybot/*/*/__pycache__' \
    --exclude='saverproxybot/*.pyc' \
    --exclude='saverproxybot/*/*.pyc' \
    --exclude='saverproxybot/*/*/*.pyc' \
    --exclude='saverproxybot/.git' \
    --exclude='saverproxybot/staticfiles' \
    --exclude='saverproxybot/media' \
    --exclude='saverproxybot/*.log' \
    saverproxybot/

echo "✅ Project files backed up"

# Backup database (if PostgreSQL)
if command -v pg_dump &> /dev/null; then
    echo "🗄️  Backing up database..."
    sudo -u postgres pg_dump saverproxybot > "$BACKUP_DIR/${BACKUP_NAME}_database.sql"
    echo "✅ Database backed up"
fi

# Backup systemd service files
echo "⚙️  Backing up service files..."
mkdir -p "$BACKUP_DIR/systemd"
cp /etc/systemd/system/saverproxybot.* "$BACKUP_DIR/systemd/" 2>/dev/null || true
echo "✅ Service files backed up"

# Backup nginx configuration
if [ -f "/etc/nginx/sites-available/saverproxybot" ]; then
    echo "🌐 Backing up nginx configuration..."
    mkdir -p "$BACKUP_DIR/nginx"
    cp /etc/nginx/sites-available/saverproxybot "$BACKUP_DIR/nginx/"
    echo "✅ Nginx configuration backed up"
fi

# Create backup info file
cat > "$BACKUP_DIR/${BACKUP_NAME}_info.txt" << EOF
SaverProxyBot Backup Information
================================
Backup Date: $(date)
Backup Name: $BACKUP_NAME
Project Directory: $PROJECT_DIR
Backup Directory: $BACKUP_DIR

Contents:
- Project files (excluding venv, cache, logs)
- Database dump (if PostgreSQL available)
- Systemd service files
- Nginx configuration (if exists)

Restore Instructions:
1. Extract project files: tar -xzf ${BACKUP_NAME}.tar.gz
2. Restore database: psql saverproxybot < ${BACKUP_NAME}_database.sql
3. Copy service files to /etc/systemd/system/
4. Copy nginx config to /etc/nginx/sites-available/
5. Run systemctl daemon-reload
6. Restart services
EOF

echo "📋 Backup info created"

# Show backup size
BACKUP_SIZE=$(du -sh "$BACKUP_DIR/$BACKUP_NAME.tar.gz" | cut -f1)
echo "📊 Backup size: $BACKUP_SIZE"

# Clean old backups (keep last 7 days)
echo "🧹 Cleaning old backups..."
find "$BACKUP_DIR" -name "saverproxybot_backup_*.tar.gz" -mtime +7 -delete
find "$BACKUP_DIR" -name "saverproxybot_backup_*_database.sql" -mtime +7 -delete
find "$BACKUP_DIR" -name "saverproxybot_backup_*_info.txt" -mtime +7 -delete

echo ""
echo "✅ Backup completed successfully!"
echo "📁 Backup location: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
echo "📋 Backup info: $BACKUP_DIR/${BACKUP_NAME}_info.txt"

# List recent backups
echo ""
echo "📚 Recent backups:"
ls -lah "$BACKUP_DIR"/saverproxybot_backup_*.tar.gz | tail -5
