[Unit]
Description=SaverProxyBot User Bots Service
After=network.target postgresql.service redis.service saverproxybot.service
Wants=saverproxybot.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/root/apps/saverproxybot
Environment=DJANGO_SETTINGS_MODULE=backend.settings
Environment=PYTHONPATH=/root/apps/saverproxybot
Environment=PYTHONUNBUFFERED=1

# Load environment variables from .env file if it exists
EnvironmentFile=-/root/apps/saverproxybot/.env

# Run the user bots management command
ExecStart=/root/apps/saverproxybot/venv/bin/python manage.py run_user_bots

# Restart settings
Restart=always
RestartSec=10
TimeoutStopSec=30

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=saverproxybot-bots

# Security settings
NoNewPrivileges=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/root/apps/saverproxybot /tmp
ProtectKernelTunables=yes
ProtectKernelModules=yes
ProtectControlGroups=yes

[Install]
WantedBy=multi-user.target
