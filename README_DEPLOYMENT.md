# SaverProxyBot Deployment Guide

This guide explains how to deploy the SaverProxyBot Django application as systemd services with Unix socket support.

## Architecture

The deployment consists of three main components:

1. **saverproxybot.socket** - Unix socket for the Django application
2. **saverproxybot.service** - Main Django application service (Gunicorn + ASGI)
3. **saverproxybot-bots.service** - Background service for Telegram user bots

## Quick Deployment

1. **Run the deployment script:**
   ```bash
   sudo chmod +x deploy.sh
   sudo ./deploy.sh
   ```

2. **Configure your environment:**
   Edit `/root/apps/saverproxybot/.env` with your actual values:
   ```bash
   sudo nano /root/apps/saverproxybot/.env
   ```

3. **Restart services after configuration:**
   ```bash
   sudo systemctl restart saverproxybot.service saverproxybot-bots.service
   ```

## Manual Deployment Steps

### 1. System Dependencies

```bash
sudo apt-get update
sudo apt-get install -y python3-pip python3-venv postgresql postgresql-contrib redis-server nginx
```

### 2. Python Dependencies

```bash
cd /root/apps/saverproxybot
source venv/bin/activate
pip install gunicorn uvicorn[standard]
deactivate
```

### 3. Create Directories

```bash
sudo mkdir -p /var/log/saverproxybot
sudo mkdir -p /run/saverproxybot
sudo chown -R www-data:www-data /var/log/saverproxybot
sudo chown -R www-data:www-data /run/saverproxybot
```

### 4. Install Service Files

```bash
sudo cp saverproxybot.socket /etc/systemd/system/
sudo cp saverproxybot.service /etc/systemd/system/
sudo cp saverproxybot-bots.service /etc/systemd/system/
sudo systemctl daemon-reload
```

### 5. Enable and Start Services

```bash
sudo systemctl enable saverproxybot.socket
sudo systemctl enable saverproxybot.service
sudo systemctl enable saverproxybot-bots.service

sudo systemctl start saverproxybot.socket
sudo systemctl start saverproxybot-bots.service
```

## Service Management

### Start Services
```bash
sudo systemctl start saverproxybot.socket saverproxybot-bots.service
```

### Stop Services
```bash
sudo systemctl stop saverproxybot.socket saverproxybot.service saverproxybot-bots.service
```

### Restart Services
```bash
sudo systemctl restart saverproxybot.service saverproxybot-bots.service
```

### Check Status
```bash
sudo systemctl status saverproxybot.socket
sudo systemctl status saverproxybot.service
sudo systemctl status saverproxybot-bots.service
```

### View Logs
```bash
# Main application logs
sudo journalctl -u saverproxybot.service -f

# Bot service logs
sudo journalctl -u saverproxybot-bots.service -f

# All logs
sudo journalctl -u saverproxybot* -f
```

## Configuration

### Environment Variables (.env)

Create and configure `/root/apps/saverproxybot/.env`:

```env
# Database Configuration
DATABASE_URL=postgresql://saverproxybot:your_password@localhost:5432/saverproxybot

# Django Settings
DEBUG=False
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Redis Configuration
REDIS_URL=redis://127.0.0.1:6379/0

# Telegram API Configuration
API_ID=your_api_id
API_HASH=your_api_hash

# Bot Configuration
X_AUTH_TOKEN=your_auth_token
```

### Database Setup

```bash
sudo -u postgres psql
CREATE DATABASE saverproxybot;
CREATE USER saverproxybot WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE saverproxybot TO saverproxybot;
\q
```

### Django Setup

```bash
cd /root/apps/saverproxybot
sudo -u www-data venv/bin/python manage.py migrate
sudo -u www-data venv/bin/python manage.py collectstatic --noinput
sudo -u www-data venv/bin/python manage.py createsuperuser
```

## Nginx Configuration

The deployment script creates an nginx configuration at `/etc/nginx/sites-available/saverproxybot`.

### SSL Setup (Production)

For production, set up SSL with Let's Encrypt:

```bash
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## Monitoring

### Service Health Check

```bash
# Check if socket is listening
sudo ss -tuln | grep saverproxybot

# Check service status
sudo systemctl is-active saverproxybot.service
sudo systemctl is-active saverproxybot-bots.service

# Check application response
curl -I http://localhost/
```

### Log Monitoring

```bash
# Monitor all services
sudo journalctl -u saverproxybot* -f

# Monitor specific service
sudo journalctl -u saverproxybot-bots.service -f --since "1 hour ago"
```

## Troubleshooting

### Common Issues

1. **Permission Denied on Socket**
   ```bash
   sudo chown www-data:www-data /run/saverproxybot/saverproxybot.sock
   sudo chmod 660 /run/saverproxybot/saverproxybot.sock
   ```

2. **Database Connection Issues**
   - Check PostgreSQL is running: `sudo systemctl status postgresql`
   - Verify database credentials in `.env`
   - Test connection: `sudo -u www-data venv/bin/python manage.py dbshell`

3. **Redis Connection Issues**
   - Check Redis is running: `sudo systemctl status redis`
   - Test connection: `redis-cli ping`

4. **Bot Service Not Starting**
   - Check Telegram session files exist
   - Verify API credentials in `.env`
   - Check logs: `sudo journalctl -u saverproxybot-bots.service -f`

### Service Restart After Changes

After modifying configuration:

```bash
sudo systemctl daemon-reload
sudo systemctl restart saverproxybot.service saverproxybot-bots.service
```

## Security Considerations

1. **File Permissions**: Services run as `www-data` user
2. **Network Security**: Socket communication instead of network ports
3. **System Isolation**: Services use systemd security features
4. **Log Rotation**: Configure logrotate for application logs

## Performance Tuning

### Gunicorn Workers

Adjust workers in `saverproxybot.service`:
```
--workers 3  # Adjust based on CPU cores
```

### Database Connections

Configure connection pooling in Django settings for high load.

### Redis Memory

Monitor Redis memory usage and configure appropriate limits.
