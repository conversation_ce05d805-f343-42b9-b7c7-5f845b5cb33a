[Unit]
Description=SaverProxyBot Django Application
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/apps/saverproxybot
Environment=DJANGO_SETTINGS_MODULE=backend.settings
Environment=PYTHONPATH=/root/apps/saverproxybot
Environment=PYTHONUNBUFFERED=1

# Load environment variables from .env file if it exists
EnvironmentFile=-/root/apps/saverproxybot/.env

# Run Django development server
ExecStart=/root/apps/saverproxybot/venv/bin/python manage.py runserver 0.0.0.0:8000

Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
