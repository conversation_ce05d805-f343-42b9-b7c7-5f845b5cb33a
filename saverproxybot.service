[Unit]
Description=SaverProxyBot Django Application
Requires=saverproxybot.socket
After=network.target postgresql.service redis.service

[Service]
Type=notify
User=www-data
Group=www-data
RuntimeDirectory=saverproxybot
WorkingDirectory=/root/apps/saverproxybot
Environment=DJANGO_SETTINGS_MODULE=backend.settings
Environment=PYTHONPATH=/root/apps/saverproxybot
Environment=PYTHONUNBUFFERED=1

# Load environment variables from .env file if it exists
EnvironmentFile=-/root/apps/saverproxybot/.env

# Use gunicorn with ASGI support for Django
ExecStart=/root/apps/saverproxybot/venv/bin/gunicorn \
    --bind unix:/run/saverproxybot/saverproxybot.sock \
    --workers 3 \
    --worker-class uvicorn.workers.UvicornWorker \
    --user www-data \
    --group www-data \
    --timeout 120 \
    --keepalive 5 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --preload \
    --access-logfile /var/log/saverproxybot/access.log \
    --error-logfile /var/log/saverproxybot/error.log \
    --log-level info \
    backend.asgi:application

ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/root/apps/saverproxybot /var/log/saverproxybot /run/saverproxybot /tmp
ProtectKernelTunables=yes
ProtectKernelModules=yes
ProtectControlGroups=yes

[Install]
WantedBy=multi-user.target
