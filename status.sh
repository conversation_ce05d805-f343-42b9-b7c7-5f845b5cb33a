#!/bin/bash

# SaverProxyBot Status Check Script

echo "🔍 SaverProxyBot Service Status"
echo "================================"

# Check systemd services
echo "📋 Systemd Services:"
echo "-------------------"

services=("saverproxybot.socket" "saverproxybot.service" "saverproxybot-bots.service")

for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        status="🟢 ACTIVE"
    else
        status="🔴 INACTIVE"
    fi
    
    if systemctl is-enabled --quiet "$service"; then
        enabled="✅ ENABLED"
    else
        enabled="❌ DISABLED"
    fi
    
    echo "  $service: $status | $enabled"
done

echo ""

# Check socket file
echo "🔌 Socket Status:"
echo "----------------"
if [ -S "/run/saverproxybot/saverproxybot.sock" ]; then
    echo "  Socket file: 🟢 EXISTS"
    ls -la /run/saverproxybot/saverproxybot.sock
else
    echo "  Socket file: 🔴 MISSING"
fi

echo ""

# Check processes
echo "🔄 Running Processes:"
echo "--------------------"
echo "Gunicorn processes:"
pgrep -f "gunicorn.*saverproxybot" | wc -l | xargs echo "  Count:"

echo "Python bot processes:"
pgrep -f "python.*run_user_bots" | wc -l | xargs echo "  Count:"

echo ""

# Check network connections
echo "🌐 Network Status:"
echo "-----------------"
if ss -tuln | grep -q saverproxybot; then
    echo "  Socket listening: 🟢 YES"
    ss -tuln | grep saverproxybot
else
    echo "  Socket listening: 🔴 NO"
fi

echo ""

# Check logs for errors
echo "📝 Recent Errors (last 10 lines):"
echo "----------------------------------"
echo "Main service errors:"
journalctl -u saverproxybot.service --since "1 hour ago" -p err --no-pager -n 5 | tail -n 5

echo ""
echo "Bot service errors:"
journalctl -u saverproxybot-bots.service --since "1 hour ago" -p err --no-pager -n 5 | tail -n 5

echo ""

# Check disk space
echo "💾 Disk Usage:"
echo "-------------"
df -h /root/apps/saverproxybot | tail -n 1

echo ""

# Check memory usage
echo "🧠 Memory Usage:"
echo "---------------"
free -h

echo ""

# Test HTTP response
echo "🌐 HTTP Test:"
echo "-------------"
if curl -s -o /dev/null -w "%{http_code}" http://localhost/ | grep -q "200\|301\|302"; then
    echo "  HTTP Response: 🟢 OK"
else
    echo "  HTTP Response: 🔴 FAILED"
fi

echo ""
echo "✅ Status check completed!"
echo ""
echo "💡 Useful commands:"
echo "  View logs: sudo journalctl -u saverproxybot.service -f"
echo "  Restart:   sudo systemctl restart saverproxybot.service saverproxybot-bots.service"
echo "  Stop:      sudo systemctl stop saverproxybot.socket saverproxybot.service saverproxybot-bots.service"
